import React, { useState, useEffect } from 'react';
import './MessengerView.css';

interface Participant {
    name: string;
}

interface Photo {
    uri: string;
    creation_timestamp: number;
}

interface Video {
    uri: string;
    creation_timestamp: number;
}

interface AudioFile {
    uri: string;
    creation_timestamp: number;
}

interface Gif {
    uri: string;
}

interface Reaction {
    reaction: string;
    actor: string;
}

interface Share {
    link: string;
    share_text?: string;
}

interface Message {
    sender_name: string;
    timestamp_ms: number;
    content?: string;
    photos?: Photo[];
    videos?: Video[];
    audio_files?: AudioFile[];
    gifs?: Gif[];
    reactions?: Reaction[];
    share?: Share;
    is_geoblocked_for_viewer: boolean;
    is_unsent_image_by_messenger_kid_parent: boolean;
}

interface MessageData {
    participants: Participant[];
    messages: Message[];
}

const MessengerView: React.FC = () => {
    const [messageData, setMessageData] = useState<MessageData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState('message_1.json');
    const [displayCount, setDisplayCount] = useState(50);
    const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');

    useEffect(() => {
        const loadMessages = async () => {
            setLoading(true);
            try {
                const response = await fetch(`/${selectedFile}`);
                if (!response.ok) {
                    throw new Error('Failed to load messages');
                }
                const data = await response.json();
                setMessageData(data);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        loadMessages();
    }, [selectedFile]);

    // Function to decode Unicode content
    const decodeUnicode = (text: string): string => {
        try {
            // Handle Facebook's Unicode encoding - multiple formats
            let decoded = text;

            // Handle \uXXXX format
            decoded = decoded.replace(/\\u([\dA-F]{4})/gi, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // Handle \u00XX format (extended ASCII)
            decoded = decoded.replace(/\\u00([\dA-F]{2})/gi, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // Handle HTML entities
            decoded = decoded.replace(/&([a-zA-Z]+);/g, (match, entity) => {
                const entities: { [key: string]: string } = {
                    amp: '&',
                    lt: '<',
                    gt: '>',
                    quot: '"',
                    apos: "'",
                    nbsp: ' ',
                };
                return entities[entity] || match;
            });

            return decoded;
        } catch {
            return text;
        }
    };

    // Function to format timestamp
    const formatTimestamp = (timestamp: number): string => {
        const date = new Date(timestamp);
        return date.toLocaleString();
    };

    // Function to extract filename from URI
    const getMediaFilename = (uri: string): string => {
        const parts = uri.split('/');
        return parts[parts.length - 1];
    };

    // Function to get media URL
    const getMediaUrl = (
        uri: string,
        type: 'photos' | 'videos' | 'audio' | 'gifs'
    ): string => {
        const filename = getMediaFilename(uri);
        return `/${type}/${filename}`;
    };

    // Function to sort messages
    const getSortedMessages = () => {
        if (!messageData) return [];
        const messages = [...messageData.messages];
        return sortOrder === 'newest'
            ? messages.sort((a, b) => b.timestamp_ms - a.timestamp_ms)
            : messages.sort((a, b) => a.timestamp_ms - b.timestamp_ms);
    };

    if (loading) {
        return <div className="messenger-loading">Loading messages...</div>;
    }

    if (error) {
        return <div className="messenger-error">Error: {error}</div>;
    }

    if (!messageData) {
        return <div className="messenger-error">No message data found</div>;
    }

    return (
        <div className="messenger-container">
            <div className="messenger-header">
                <div className="header-top">
                    <h2>Group Chat</h2>
                    <div className="header-controls">
                        <div className="file-selector">
                            <select
                                value={selectedFile}
                                onChange={(e) => {
                                    setSelectedFile(e.target.value);
                                    setDisplayCount(50); // Reset display count when switching files
                                }}
                                className="file-select"
                            >
                                <option value="message_1.json">
                                    Message File 1
                                </option>
                                <option value="message_2.json">
                                    Message File 2
                                </option>
                                <option value="message_3.json">
                                    Message File 3
                                </option>
                            </select>
                        </div>
                        <button
                            className="sort-btn"
                            onClick={() =>
                                setSortOrder((prev) =>
                                    prev === 'newest' ? 'oldest' : 'newest'
                                )
                            }
                            title={`Currently showing ${sortOrder} first`}
                        >
                            {sortOrder === 'newest' ? '↓' : '↑'}
                        </button>
                    </div>
                </div>
                <div className="participants">
                    {messageData.participants.map((participant, index) => (
                        <span key={index} className="participant">
                            {decodeUnicode(participant.name)}
                        </span>
                    ))}
                </div>
            </div>

            <div className="messages-container">
                {getSortedMessages()
                    .slice(0, displayCount)
                    .map((message, index) => (
                        <div key={index} className="message">
                            <div className="message-header">
                                <span className="sender-name">
                                    {decodeUnicode(message.sender_name)}
                                </span>
                                <span className="timestamp">
                                    {formatTimestamp(message.timestamp_ms)}
                                </span>
                            </div>

                            <div className="message-content">
                                {/* Text content */}
                                {message.content && (
                                    <div className="text-content">
                                        {decodeUnicode(message.content)}
                                    </div>
                                )}

                                {/* Photos */}
                                {message.photos &&
                                    message.photos.map((photo, photoIndex) => (
                                        <div
                                            key={photoIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    photo.uri,
                                                    'photos'
                                                )}
                                                alt="Shared photo"
                                                className="message-photo"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Videos */}
                                {message.videos &&
                                    message.videos.map((video, videoIndex) => (
                                        <div
                                            key={videoIndex}
                                            className="media-item"
                                        >
                                            <video
                                                src={getMediaUrl(
                                                    video.uri,
                                                    'videos'
                                                )}
                                                controls
                                                className="message-video"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Audio files */}
                                {message.audio_files &&
                                    message.audio_files.map(
                                        (audio, audioIndex) => (
                                            <div
                                                key={audioIndex}
                                                className="media-item"
                                            >
                                                <audio
                                                    src={getMediaUrl(
                                                        audio.uri,
                                                        'audio'
                                                    )}
                                                    controls
                                                    className="message-audio"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display =
                                                            'none';
                                                    }}
                                                />
                                            </div>
                                        )
                                    )}

                                {/* GIFs */}
                                {message.gifs &&
                                    message.gifs.map((gif, gifIndex) => (
                                        <div
                                            key={gifIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    gif.uri,
                                                    'gifs'
                                                )}
                                                alt="GIF"
                                                className="message-gif"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Shared links */}
                                {message.share && (
                                    <div className="shared-link">
                                        <a
                                            href={message.share.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {message.share.share_text
                                                ? decodeUnicode(
                                                      message.share.share_text
                                                  )
                                                : message.share.link}
                                        </a>
                                    </div>
                                )}
                            </div>

                            {/* Reactions */}
                            {message.reactions &&
                                message.reactions.length > 0 && (
                                    <div className="message-reactions">
                                        {message.reactions.map(
                                            (reaction, reactionIndex) => (
                                                <span
                                                    key={reactionIndex}
                                                    className="reaction"
                                                >
                                                    {reaction.reaction}{' '}
                                                    {decodeUnicode(
                                                        reaction.actor
                                                    )}
                                                </span>
                                            )
                                        )}
                                    </div>
                                )}
                        </div>
                    ))}

                {displayCount < getSortedMessages().length && (
                    <div className="load-more-container">
                        <button
                            className="load-more-btn"
                            onClick={() => setDisplayCount((prev) => prev + 50)}
                        >
                            Load More Messages (
                            {getSortedMessages().length - displayCount}{' '}
                            remaining)
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MessengerView;

import React, { useState, useEffect } from 'react';
import './MessengerView.css';

interface Participant {
    name: string;
}

interface Photo {
    uri: string;
    creation_timestamp: number;
}

interface Video {
    uri: string;
    creation_timestamp: number;
}

interface AudioFile {
    uri: string;
    creation_timestamp: number;
}

interface Gif {
    uri: string;
}

interface Reaction {
    reaction: string;
    actor: string;
}

interface Share {
    link: string;
    share_text?: string;
}

interface Message {
    sender_name: string;
    timestamp_ms: number;
    content?: string;
    photos?: Photo[];
    videos?: Video[];
    audio_files?: AudioFile[];
    gifs?: Gif[];
    reactions?: Reaction[];
    share?: Share;
    is_geoblocked_for_viewer: boolean;
    is_unsent_image_by_messenger_kid_parent: boolean;
}

interface MessageData {
    participants: Participant[];
    messages: Message[];
}

const MessengerView: React.FC = () => {
    const [messageData, setMessageData] = useState<MessageData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState('message_1.json');
    const [displayCount, setDisplayCount] = useState(50);
    const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');

    useEffect(() => {
        const loadMessages = async () => {
            setLoading(true);
            try {
                const response = await fetch(`/${selectedFile}`);
                if (!response.ok) {
                    throw new Error('Failed to load messages');
                }
                const data = await response.json();
                setMessageData(data);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        loadMessages();
    }, [selectedFile]);

    // Function to decode Unicode content for Vietnamese text
    const decodeUnicode = (text: string): string => {
        try {
            let decoded = text;

            // Handle Facebook's Unicode escape sequences
            decoded = decoded.replace(
                /\\u00([0-9a-fA-F]{2})/g,
                (match, hex) => {
                    return String.fromCharCode(parseInt(hex, 16));
                }
            );

            decoded = decoded.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // Handle common Vietnamese character encoding issues
            const vietnameseMap: { [key: string]: string } = {
                'Ã¡': 'á', 'Ã ': 'à', 'áº£': 'ả', 'Ã£': 'ã', 'áº¡': 'ạ',
                'Ã¢': 'â', 'áº¥': 'ấ', 'áº§': 'ầ', 'áº©': 'ẩ', 'áº«': 'ẫ', 'áº­': 'ậ',
                'Äƒ': 'ă', 'áº¯': 'ắ', 'áº±': 'ằ', 'áº³': 'ẳ', 'áºµ': 'ẵ', 'áº·': 'ặ',
                'Ã©': 'é', 'Ã¨': 'è', 'áº»': 'ẻ', 'áº½': 'ẽ', 'áº¹': 'ẹ',
                'Ãª': 'ê', 'áº¿': 'ế', 'á»': 'ề', 'á»ƒ': 'ể', 'á»…': 'ễ', 'á»‡': 'ệ',
                'Ã­': 'í', 'Ã¬': 'ì', 'á»‰': 'ỉ', 'Ä©': 'ĩ', 'á»‹': 'ị',
                'Ã³': 'ó', 'Ã²': 'ò', 'á»': 'ỏ', 'Ãµ': 'õ', 'á»': 'ọ',
                'Ã´': 'ô', 'á»'': 'ố', 'á»"': 'ồ', 'á»•': 'ổ', 'á»—': 'ỗ', 'á»™': 'ộ',
                'Æ¡': 'ơ', 'á»›': 'ớ', 'á»': 'ờ', 'á»Ÿ': 'ở', 'á»¡': 'ỡ', 'á»£': 'ợ',
                'Ãº': 'ú', 'Ã¹': 'ù', 'á»§': 'ủ', 'Å©': 'ũ', 'á»¥': 'ụ',
                'Æ°': 'ư', 'á»©': 'ứ', 'á»«': 'ừ', 'á»­': 'ử', 'á»¯': 'ữ', 'á»±': 'ự',
                'Ã½': 'ý', 'á»³': 'ỳ', 'á»·': 'ỷ', 'á»¹': 'ỹ', 'á»µ': 'ỵ',
                'Ä'': 'đ',
                // Uppercase versions
                'Ã': 'Á', 'Ã€': 'À', 'áº¢': 'Ả', 'Ãƒ': 'Ã', 'áº ': 'Ạ',
                'Ã‚': 'Â', 'áº¤': 'Ấ', 'áº¦': 'Ầ', 'áº¨': 'Ẩ', 'áºª': 'Ẫ', 'áº¬': 'Ậ',
                'Ä‚': 'Ă', 'áº®': 'Ắ', 'áº°': 'Ằ', 'áº²': 'Ẳ', 'áº´': 'Ẵ', 'áº¶': 'Ặ',
                'Ã‰': 'É', 'Ãˆ': 'È', 'áºº': 'Ẻ', 'áº¼': 'Ẽ', 'áº¸': 'Ẹ',
                'ÃŠ': 'Ê', 'áº¾': 'Ế', 'á»€': 'Ề', 'á»‚': 'Ể', 'á»„': 'Ễ', 'á»†': 'Ệ',
                'Ã': 'Í', 'ÃŒ': 'Ì', 'á»ˆ': 'Ỉ', 'Ä¨': 'Ĩ', 'á»Š': 'Ị',
                'Ã"': 'Ó', 'Ã'': 'Ò', 'á»Ž': 'Ỏ', 'Ã•': 'Õ', 'á»Œ': 'Ọ',
                'Ã"': 'Ô', 'á»': 'Ố', 'á»'': 'Ồ', 'á»"': 'Ổ', 'á»–': 'Ỗ', 'á»˜': 'Ộ',
                'Æ ': 'Ơ', 'á»š': 'Ớ', 'á»œ': 'Ờ', 'á»ž': 'Ở', 'á» ': 'Ỡ', 'á»¢': 'Ợ',
                'Ãš': 'Ú', 'Ã™': 'Ù', 'á»¦': 'Ủ', 'Å¨': 'Ũ', 'á»¤': 'Ụ',
                'Æ¯': 'Ư', 'á»¨': 'Ứ', 'á»ª': 'Ừ', 'á»¬': 'Ử', 'á»®': 'Ữ', 'á»°': 'Ự',
                'Ã': 'Ý', 'á»²': 'Ỳ', 'á»¶': 'Ỷ', 'á»¸': 'Ỹ', 'á»´': 'Ỵ',
                'Ä': 'Đ'
            };

            // Apply Vietnamese character mappings
            for (const [encoded, vietnamese] of Object.entries(vietnameseMap)) {
                decoded = decoded.replace(new RegExp(encoded, 'g'), vietnamese);
            }

            // Handle double-encoded UTF-8 sequences
            try {
                const bytes = [];
                for (let i = 0; i < decoded.length; i++) {
                    bytes.push(decoded.charCodeAt(i) & 0xff);
                }

                const utf8Decoded = new TextDecoder('utf-8', { fatal: false }).decode(new Uint8Array(bytes));

                // If UTF-8 decoding produces Vietnamese characters, use it
                if (utf8Decoded !== decoded && /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(utf8Decoded)) {
                    decoded = utf8Decoded;
                }
            } catch {
                // Keep original if UTF-8 decoding fails
            }

            // Handle HTML entities
            decoded = decoded.replace(/&([a-zA-Z]+);/g, (match, entity) => {
                const entities: { [key: string]: string } = {
                    amp: '&', lt: '<', gt: '>', quot: '"', apos: "'", nbsp: ' ',
                };
                return entities[entity] || match;
            });

            return decoded;
        } catch (error) {
            console.warn('Unicode decoding failed:', error);
            return text;
        }
    };

    // Function to format timestamp
    const formatTimestamp = (timestamp: number): string => {
        const date = new Date(timestamp);
        return date.toLocaleString();
    };

    // Function to extract filename from URI
    const getMediaFilename = (uri: string): string => {
        const parts = uri.split('/');
        return parts[parts.length - 1];
    };

    // Function to get media URL
    const getMediaUrl = (
        uri: string,
        type: 'photos' | 'videos' | 'audio' | 'gifs'
    ): string => {
        const filename = getMediaFilename(uri);
        return `/${type}/${filename}`;
    };

    // Function to sort messages
    const getSortedMessages = () => {
        if (!messageData) return [];
        const messages = [...messageData.messages];
        return sortOrder === 'newest'
            ? messages.sort((a, b) => b.timestamp_ms - a.timestamp_ms)
            : messages.sort((a, b) => a.timestamp_ms - b.timestamp_ms);
    };

    if (loading) {
        return <div className="messenger-loading">Loading messages...</div>;
    }

    if (error) {
        return <div className="messenger-error">Error: {error}</div>;
    }

    if (!messageData) {
        return <div className="messenger-error">No message data found</div>;
    }

    return (
        <div className="messenger-container">
            <div className="messenger-header">
                <div className="header-top">
                    <h2>Group Chat</h2>
                    <div className="header-controls">
                        <div className="file-selector">
                            <select
                                value={selectedFile}
                                onChange={(e) => {
                                    setSelectedFile(e.target.value);
                                    setDisplayCount(50); // Reset display count when switching files
                                }}
                                className="file-select"
                            >
                                <option value="message_1.json">
                                    Message File 1
                                </option>
                                <option value="message_2.json">
                                    Message File 2
                                </option>
                                <option value="message_3.json">
                                    Message File 3
                                </option>
                            </select>
                        </div>
                        <button
                            className="sort-btn"
                            onClick={() =>
                                setSortOrder((prev) =>
                                    prev === 'newest' ? 'oldest' : 'newest'
                                )
                            }
                            title={`Currently showing ${sortOrder} first`}
                        >
                            {sortOrder === 'newest' ? '↓' : '↑'}
                        </button>
                    </div>
                </div>
                <div className="participants">
                    {messageData.participants.map((participant, index) => (
                        <span key={index} className="participant">
                            {decodeUnicode(participant.name)}
                        </span>
                    ))}
                </div>
            </div>

            <div className="messages-container">
                {getSortedMessages()
                    .slice(0, displayCount)
                    .map((message, index) => (
                        <div key={index} className="message">
                            <div className="message-header">
                                <span className="sender-name">
                                    {decodeUnicode(message.sender_name)}
                                </span>
                                <span className="timestamp">
                                    {formatTimestamp(message.timestamp_ms)}
                                </span>
                            </div>

                            <div className="message-content">
                                {/* Text content */}
                                {message.content && (
                                    <div className="text-content">
                                        {decodeUnicode(message.content)}
                                    </div>
                                )}

                                {/* Photos */}
                                {message.photos &&
                                    message.photos.map((photo, photoIndex) => (
                                        <div
                                            key={photoIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    photo.uri,
                                                    'photos'
                                                )}
                                                alt="Shared photo"
                                                className="message-photo"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Videos */}
                                {message.videos &&
                                    message.videos.map((video, videoIndex) => (
                                        <div
                                            key={videoIndex}
                                            className="media-item"
                                        >
                                            <video
                                                src={getMediaUrl(
                                                    video.uri,
                                                    'videos'
                                                )}
                                                controls
                                                className="message-video"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Audio files */}
                                {message.audio_files &&
                                    message.audio_files.map(
                                        (audio, audioIndex) => (
                                            <div
                                                key={audioIndex}
                                                className="media-item"
                                            >
                                                <audio
                                                    src={getMediaUrl(
                                                        audio.uri,
                                                        'audio'
                                                    )}
                                                    controls
                                                    className="message-audio"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display =
                                                            'none';
                                                    }}
                                                />
                                            </div>
                                        )
                                    )}

                                {/* GIFs */}
                                {message.gifs &&
                                    message.gifs.map((gif, gifIndex) => (
                                        <div
                                            key={gifIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    gif.uri,
                                                    'gifs'
                                                )}
                                                alt="GIF"
                                                className="message-gif"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Shared links */}
                                {message.share && (
                                    <div className="shared-link">
                                        <a
                                            href={message.share.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {message.share.share_text
                                                ? decodeUnicode(
                                                      message.share.share_text
                                                  )
                                                : message.share.link}
                                        </a>
                                    </div>
                                )}
                            </div>

                            {/* Reactions */}
                            {message.reactions &&
                                message.reactions.length > 0 && (
                                    <div className="message-reactions">
                                        {message.reactions.map(
                                            (reaction, reactionIndex) => (
                                                <span
                                                    key={reactionIndex}
                                                    className="reaction"
                                                >
                                                    {reaction.reaction}{' '}
                                                    {decodeUnicode(
                                                        reaction.actor
                                                    )}
                                                </span>
                                            )
                                        )}
                                    </div>
                                )}
                        </div>
                    ))}

                {displayCount < getSortedMessages().length && (
                    <div className="load-more-container">
                        <button
                            className="load-more-btn"
                            onClick={() => setDisplayCount((prev) => prev + 50)}
                        >
                            Load More Messages (
                            {getSortedMessages().length - displayCount}{' '}
                            remaining)
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MessengerView;

.messenger-container {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 90vh;
    display: flex;
    flex-direction: column;
}

.messenger-header {
    background: #0084ff;
    color: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.messenger-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-selector {
    display: flex;
    align-items: center;
}

.file-select {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
}

.file-select option {
    background: #0084ff;
    color: white;
}

.sort-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sort-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.participants {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 14px;
    opacity: 0.9;
}

.participant {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 16px;
    background: white;
    border-radius: 18px;
    padding: 12px 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    max-width: 70%;
    word-wrap: break-word;
}

.message:nth-child(even) {
    margin-left: auto;
    background: #0084ff;
    color: white;
}

.message:nth-child(even) .sender-name {
    color: rgba(255, 255, 255, 0.8);
}

.message:nth-child(even) .timestamp {
    color: rgba(255, 255, 255, 0.6);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
}

.sender-name {
    font-weight: 600;
    color: #0084ff;
}

.timestamp {
    color: #65676b;
    font-size: 11px;
}

.message-content {
    line-height: 1.4;
}

.text-content {
    margin-bottom: 8px;
    white-space: pre-wrap;
    word-break: break-word;
}

.media-item {
    margin: 8px 0;
}

.message-photo,
.message-gif {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
}

.message-photo:hover,
.message-gif:hover {
    transform: scale(1.02);
}

.message-video {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    background: #000;
}

.message-audio {
    width: 100%;
    max-width: 300px;
    height: 40px;
}

.shared-link {
    margin: 8px 0;
    padding: 12px;
    background: rgba(0, 132, 255, 0.1);
    border-radius: 8px;
    border-left: 3px solid #0084ff;
}

.shared-link a {
    color: #0084ff;
    text-decoration: none;
    font-weight: 500;
}

.shared-link a:hover {
    text-decoration: underline;
}

.message-reactions {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.reaction {
    background: rgba(0, 132, 255, 0.1);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 11px;
    color: #0084ff;
}

.messenger-loading,
.messenger-error {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
    font-size: 18px;
    color: #65676b;
}

.messenger-error {
    color: #e41e3f;
}

.load-more-container {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.load-more-btn {
    background: #0084ff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.load-more-btn:hover {
    background: #0066cc;
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
    .messenger-container {
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .message {
        max-width: 85%;
    }

    .messenger-header {
        padding: 12px 16px;
    }

    .messages-container {
        padding: 12px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .messenger-container {
        background: #242526;
        color: #e4e6ea;
    }

    .messages-container {
        background: #18191a;
    }

    .message {
        background: #3a3b3c;
        color: #e4e6ea;
    }

    .message:nth-child(even) {
        background: #0084ff;
        color: white;
    }

    .sender-name {
        color: #0084ff;
    }

    .timestamp {
        color: #b0b3b8;
    }

    .shared-link {
        background: rgba(0, 132, 255, 0.2);
    }

    .reaction {
        background: rgba(0, 132, 255, 0.2);
    }
}

# Messenger-like Message Viewer

This React application displays Facebook Messenger message data in a chat-like interface similar to the original Messenger app.

## Features

- **Unicode Decoding**: Automatically decodes Facebook's Unicode-encoded content to display proper Vietnamese and other international characters
- **Media Support**: Displays photos, videos, audio files, and GIFs from the public folder
- **Multiple Message Files**: Switch between different message JSON files using the dropdown selector
- **Pagination**: Load more messages with the "Load More" button (loads 50 messages at a time)
- **Messenger-like UI**: Clean, modern interface that resembles Facebook Messenger
- **Responsive Design**: Works on both desktop and mobile devices
- **Dark Mode Support**: Automatically adapts to system dark mode preferences

## File Structure

```
public/
├── message_1.json          # Main message data file
├── message_2.json          # Additional message files
├── message_3.json
├── photos/                 # Photo attachments
├── videos/                 # Video attachments  
├── audio/                  # Audio message files
└── gifs/                   # GIF attachments

src/
├── components/
│   ├── MessengerView.tsx   # Main component
│   └── MessengerView.css   # Styling
└── App.tsx                 # Root component
```

## Message Data Format

The application expects JSON files with the following structure:

```json
{
  "participants": [
    {
      "name": "Participant Name"
    }
  ],
  "messages": [
    {
      "sender_name": "Sender Name",
      "timestamp_ms": 1234567890123,
      "content": "Message text with \\u00XX Unicode encoding",
      "photos": [
        {
          "uri": "path/to/photo.jpg",
          "creation_timestamp": 1234567890
        }
      ],
      "videos": [
        {
          "uri": "path/to/video.mp4", 
          "creation_timestamp": 1234567890
        }
      ],
      "audio_files": [
        {
          "uri": "path/to/audio.mp4",
          "creation_timestamp": 1234567890
        }
      ],
      "gifs": [
        {
          "uri": "path/to/gif.gif"
        }
      ],
      "reactions": [
        {
          "reaction": "😂",
          "actor": "Reactor Name"
        }
      ],
      "share": {
        "link": "https://example.com",
        "share_text": "Shared content description"
      }
    }
  ]
}
```

## Usage

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Switch between message files**: Use the dropdown in the header to select different message JSON files

3. **Load more messages**: Click the "Load More Messages" button to display additional messages (50 at a time)

4. **View media**: Photos, videos, and GIFs are automatically loaded from the public folder based on the file paths in the JSON

## Media File Mapping

The application automatically maps media URIs to the public folder:
- `your_facebook_activity/messages/inbox/.../photos/123.jpg` → `/photos/123.jpg`
- `your_facebook_activity/messages/inbox/.../videos/456.mp4` → `/videos/456.mp4`
- `your_facebook_activity/messages/inbox/.../audio/789.mp4` → `/audio/789.mp4`
- `your_facebook_activity/messages/inbox/.../gifs/abc.gif` → `/gifs/abc.gif`

## Unicode Decoding

The application handles Facebook's Unicode encoding formats:
- `\\uXXXX` format for 4-digit hex codes
- `\\u00XX` format for 2-digit hex codes  
- HTML entities like `&amp;`, `&lt;`, etc.

This ensures proper display of Vietnamese text and other international characters.

## Styling

The interface uses a Messenger-like design with:
- Blue (#0084ff) primary color
- Alternating message bubbles (white and blue)
- Rounded corners and subtle shadows
- Responsive layout for mobile devices
- Dark mode support

## Error Handling

- Missing media files are gracefully hidden
- Failed JSON loads show error messages
- Unicode decoding failures fall back to original text
- Network errors are displayed to the user

import React, { useState, useEffect } from 'react';
import './MessengerView.css';

interface Participant {
    name: string;
}

interface Photo {
    uri: string;
    creation_timestamp: number;
}

interface Video {
    uri: string;
    creation_timestamp: number;
}

interface AudioFile {
    uri: string;
    creation_timestamp: number;
}

interface Gif {
    uri: string;
}

interface Reaction {
    reaction: string;
    actor: string;
}

interface Share {
    link: string;
    share_text?: string;
}

interface Message {
    sender_name: string;
    timestamp_ms: number;
    content?: string;
    photos?: Photo[];
    videos?: Video[];
    audio_files?: AudioFile[];
    gifs?: Gif[];
    reactions?: Reaction[];
    share?: Share;
    is_geoblocked_for_viewer: boolean;
    is_unsent_image_by_messenger_kid_parent: boolean;
}

interface MessageData {
    participants: Participant[];
    messages: Message[];
}

const MessengerView: React.FC = () => {
    const [messageData, setMessageData] = useState<MessageData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState('message_1.json');
    const [displayCount, setDisplayCount] = useState(50);
    const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');

    useEffect(() => {
        const loadMessages = async () => {
            setLoading(true);
            try {
                const response = await fetch(`/${selectedFile}`);
                if (!response.ok) {
                    throw new Error('Failed to load messages');
                }
                const data = await response.json();
                setMessageData(data);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        loadMessages();
    }, [selectedFile]);

    // Function to decode Unicode content for Vietnamese text
    const decodeUnicode = (text: string): string => {
        try {
            let decoded = text;

            // Handle Facebook's Unicode escape sequences
            decoded = decoded.replace(
                /\\u00([0-9a-fA-F]{2})/g,
                (match, hex) => {
                    return String.fromCharCode(parseInt(hex, 16));
                }
            );

            decoded = decoded.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // Handle common Vietnamese character encoding issues with a simpler approach
            // Apply common Vietnamese character fixes
            decoded = decoded
                .replace(/Ã¡/g, 'á')
                .replace(/Ã /g, 'à')
                .replace(/áº£/g, 'ả')
                .replace(/Ã£/g, 'ã')
                .replace(/áº¡/g, 'ạ');
            decoded = decoded
                .replace(/Ã¢/g, 'â')
                .replace(/áº¥/g, 'ấ')
                .replace(/áº§/g, 'ầ')
                .replace(/áº©/g, 'ẩ')
                .replace(/áº«/g, 'ẫ')
                .replace(/áº­/g, 'ậ');
            decoded = decoded
                .replace(/Äƒ/g, 'ă')
                .replace(/áº¯/g, 'ắ')
                .replace(/áº±/g, 'ằ')
                .replace(/áº³/g, 'ẳ')
                .replace(/áºµ/g, 'ẵ')
                .replace(/áº·/g, 'ặ');
            decoded = decoded
                .replace(/Ã©/g, 'é')
                .replace(/Ã¨/g, 'è')
                .replace(/áº»/g, 'ẻ')
                .replace(/áº½/g, 'ẽ')
                .replace(/áº¹/g, 'ẹ');
            decoded = decoded
                .replace(/Ãª/g, 'ê')
                .replace(/áº¿/g, 'ế')
                .replace(/á»/g, 'ề')
                .replace(/á»ƒ/g, 'ể')
                .replace(/á»…/g, 'ễ')
                .replace(/á»‡/g, 'ệ');
            decoded = decoded
                .replace(/Ã­/g, 'í')
                .replace(/Ã¬/g, 'ì')
                .replace(/á»‰/g, 'ỉ')
                .replace(/Ä©/g, 'ĩ')
                .replace(/á»‹/g, 'ị');
            decoded = decoded
                .replace(/Ã³/g, 'ó')
                .replace(/Ã²/g, 'ò')
                .replace(/á»/g, 'ỏ')
                .replace(/Ãµ/g, 'õ')
                .replace(/á»/g, 'ọ');
            decoded = decoded
                .replace(/Ã´/g, 'ô')
                .replace(/á»'/g, 'ố')
                .replace(/á»"/g, 'ồ')
                .replace(/á»•/g, 'ổ')
                .replace(/á»—/g, 'ỗ')
                .replace(/á»™/g, 'ộ');
            decoded = decoded
                .replace(/Æ¡/g, 'ơ')
                .replace(/á»›/g, 'ớ')
                .replace(/á»/g, 'ờ')
                .replace(/á»Ÿ/g, 'ở')
                .replace(/á»¡/g, 'ỡ')
                .replace(/á»£/g, 'ợ');
            decoded = decoded
                .replace(/Ãº/g, 'ú')
                .replace(/Ã¹/g, 'ù')
                .replace(/á»§/g, 'ủ')
                .replace(/Å©/g, 'ũ')
                .replace(/á»¥/g, 'ụ');
            decoded = decoded
                .replace(/Æ°/g, 'ư')
                .replace(/á»©/g, 'ứ')
                .replace(/á»«/g, 'ừ')
                .replace(/á»­/g, 'ử')
                .replace(/á»¯/g, 'ữ')
                .replace(/á»±/g, 'ự');
            decoded = decoded
                .replace(/Ã½/g, 'ý')
                .replace(/á»³/g, 'ỳ')
                .replace(/á»·/g, 'ỷ')
                .replace(/á»¹/g, 'ỹ')
                .replace(/á»µ/g, 'ỵ');
            decoded = decoded.replace(/Ä'/g, 'đ').replace(/Ä/g, 'Đ');

            // Handle double-encoded UTF-8 sequences
            try {
                const bytes = [];
                for (let i = 0; i < decoded.length; i++) {
                    bytes.push(decoded.charCodeAt(i) & 0xff);
                }

                const utf8Decoded = new TextDecoder('utf-8', {
                    fatal: false,
                }).decode(new Uint8Array(bytes));

                // If UTF-8 decoding produces Vietnamese characters, use it
                if (
                    utf8Decoded !== decoded &&
                    /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(
                        utf8Decoded
                    )
                ) {
                    decoded = utf8Decoded;
                }
            } catch {
                // Keep original if UTF-8 decoding fails
            }

            // Handle HTML entities
            decoded = decoded.replace(/&([a-zA-Z]+);/g, (match, entity) => {
                const entities: { [key: string]: string } = {
                    amp: '&',
                    lt: '<',
                    gt: '>',
                    quot: '"',
                    apos: "'",
                    nbsp: ' ',
                };
                return entities[entity] || match;
            });

            return decoded;
        } catch (error) {
            console.warn('Unicode decoding failed:', error);
            return text;
        }
    };

    // Function to format timestamp
    const formatTimestamp = (timestamp: number): string => {
        const date = new Date(timestamp);
        return date.toLocaleString();
    };

    // Function to extract filename from URI
    const getMediaFilename = (uri: string): string => {
        const parts = uri.split('/');
        return parts[parts.length - 1];
    };

    // Function to get media URL
    const getMediaUrl = (
        uri: string,
        type: 'photos' | 'videos' | 'audio' | 'gifs'
    ): string => {
        const filename = getMediaFilename(uri);
        return `/${type}/${filename}`;
    };

    // Function to sort messages
    const getSortedMessages = () => {
        if (!messageData) return [];
        const messages = [...messageData.messages];
        return sortOrder === 'newest'
            ? messages.sort((a, b) => b.timestamp_ms - a.timestamp_ms)
            : messages.sort((a, b) => a.timestamp_ms - b.timestamp_ms);
    };

    if (loading) {
        return <div className="messenger-loading">Loading messages...</div>;
    }

    if (error) {
        return <div className="messenger-error">Error: {error}</div>;
    }

    if (!messageData) {
        return <div className="messenger-error">No message data found</div>;
    }

    return (
        <div className="messenger-container">
            <div className="messenger-header">
                <div className="header-top">
                    <h2>Group Chat</h2>
                    <div className="header-controls">
                        <div className="file-selector">
                            <select
                                value={selectedFile}
                                onChange={(e) => {
                                    setSelectedFile(e.target.value);
                                    setDisplayCount(50); // Reset display count when switching files
                                }}
                                className="file-select"
                            >
                                <option value="message_1.json">
                                    Message File 1
                                </option>
                                <option value="message_2.json">
                                    Message File 2
                                </option>
                                <option value="message_3.json">
                                    Message File 3
                                </option>
                            </select>
                        </div>
                        <button
                            className="sort-btn"
                            onClick={() =>
                                setSortOrder((prev) =>
                                    prev === 'newest' ? 'oldest' : 'newest'
                                )
                            }
                            title={`Currently showing ${sortOrder} first`}
                        >
                            {sortOrder === 'newest' ? '↓' : '↑'}
                        </button>
                    </div>
                </div>
                <div className="participants">
                    {messageData.participants.map((participant, index) => (
                        <span key={index} className="participant">
                            {decodeUnicode(participant.name)}
                        </span>
                    ))}
                </div>
            </div>

            <div className="messages-container">
                {getSortedMessages()
                    .slice(0, displayCount)
                    .map((message, index) => (
                        <div key={index} className="message">
                            <div className="message-header">
                                <span className="sender-name">
                                    {decodeUnicode(message.sender_name)}
                                </span>
                                <span className="timestamp">
                                    {formatTimestamp(message.timestamp_ms)}
                                </span>
                            </div>

                            <div className="message-content">
                                {/* Text content */}
                                {message.content && (
                                    <div className="text-content">
                                        {decodeUnicode(message.content)}
                                    </div>
                                )}

                                {/* Photos */}
                                {message.photos &&
                                    message.photos.map((photo, photoIndex) => (
                                        <div
                                            key={photoIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    photo.uri,
                                                    'photos'
                                                )}
                                                alt="Shared photo"
                                                className="message-photo"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Videos */}
                                {message.videos &&
                                    message.videos.map((video, videoIndex) => (
                                        <div
                                            key={videoIndex}
                                            className="media-item"
                                        >
                                            <video
                                                src={getMediaUrl(
                                                    video.uri,
                                                    'videos'
                                                )}
                                                controls
                                                className="message-video"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Audio files */}
                                {message.audio_files &&
                                    message.audio_files.map(
                                        (audio, audioIndex) => (
                                            <div
                                                key={audioIndex}
                                                className="media-item"
                                            >
                                                <audio
                                                    src={getMediaUrl(
                                                        audio.uri,
                                                        'audio'
                                                    )}
                                                    controls
                                                    className="message-audio"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display =
                                                            'none';
                                                    }}
                                                />
                                            </div>
                                        )
                                    )}

                                {/* GIFs */}
                                {message.gifs &&
                                    message.gifs.map((gif, gifIndex) => (
                                        <div
                                            key={gifIndex}
                                            className="media-item"
                                        >
                                            <img
                                                src={getMediaUrl(
                                                    gif.uri,
                                                    'gifs'
                                                )}
                                                alt="GIF"
                                                className="message-gif"
                                                onError={(e) => {
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        </div>
                                    ))}

                                {/* Shared links */}
                                {message.share && (
                                    <div className="shared-link">
                                        <a
                                            href={message.share.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {message.share.share_text
                                                ? decodeUnicode(
                                                      message.share.share_text
                                                  )
                                                : message.share.link}
                                        </a>
                                    </div>
                                )}
                            </div>

                            {/* Reactions */}
                            {message.reactions &&
                                message.reactions.length > 0 && (
                                    <div className="message-reactions">
                                        {message.reactions.map(
                                            (reaction, reactionIndex) => (
                                                <span
                                                    key={reactionIndex}
                                                    className="reaction"
                                                >
                                                    {reaction.reaction}{' '}
                                                    {decodeUnicode(
                                                        reaction.actor
                                                    )}
                                                </span>
                                            )
                                        )}
                                    </div>
                                )}
                        </div>
                    ))}

                {displayCount < getSortedMessages().length && (
                    <div className="load-more-container">
                        <button
                            className="load-more-btn"
                            onClick={() => setDisplayCount((prev) => prev + 50)}
                        >
                            Load More Messages (
                            {getSortedMessages().length - displayCount}{' '}
                            remaining)
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MessengerView;
